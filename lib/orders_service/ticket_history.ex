defmodule OrdersService.TicketHistory do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query

  alias Ecto.Multi
  alias OrdersService.Repo
  alias OrdersService.Ticket
  alias OrdersService.TicketHistory

  # styler:sort
  @actor_types [
    :SYSTEM,
    :USER
  ]

  # styler:sort
  @ticket_status [
    :ACTIVE,
    :CREATED,
    :DEFRAUDED,
    :FAILED,
    :INVITATION_REJECTED,
    :PENDING,
    :REFUNDED,
    :REFUNDING,
    :SWAPPED,
    :TIMEDOUT,
    :UNUSED,
    :USED
  ]

  # styler:sort
  @location_types [
    :ENTRANCE_AREA,
    :OFFICE
  ]
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  @schema_prefix "orders"
  # styler:sort
  schema "ticket_histories" do
    belongs_to(:ticket, Ticket, foreign_key: :ticket_id)

    field :actor_document_id, :string
    field :actor_id, Ecto.UUID
    field :actor_type, Ecto.Enum, values: @actor_types
    field :location_id, Ecto.UUID
    field :location_type, Ecto.Enum, values: @location_types
    field :metadata, :map
    field :status_after, Ecto.Enum, values: @ticket_status
    field :status_before, Ecto.Enum, values: @ticket_status

    timestamps(updated_at: false, type: :utc_datetime)
  end

  @type t :: %__MODULE__{
          actor_document_id: String.t(),
          actor_id: Ecto.UUID.t(),
          actor_type: atom(),
          id: Ecto.UUID.t(),
          inserted_at: DateTime.t(),
          location_id: Ecto.UUID.t(),
          location_type: atom(),
          metadata: map(),
          status_after: atom(),
          status_before: atom(),
          ticket_id: Ecto.UUID.t()
        }

  # TODO: remove after refactor order process. It's a workaround to handle the history in Ecto Multis
  @spec changeset(TicketHistory.t(), map()) :: Ecto.Changeset.t()
  def changeset(ticket_history, attrs) do
    ticket_history
    |> cast(attrs, [
      :actor_document_id,
      :actor_id,
      :ticket_id,
      :actor_type,
      :location_id,
      :location_type,
      :metadata,
      :status_before,
      :status_after,
      :inserted_at
    ])
    |> validate_required([
      :ticket_id,
      :actor_type,
      :status_before,
      :status_after,
      :inserted_at
    ])
    |> validate_inclusion(:actor_type, @actor_types)
    |> validate_inclusion(:status_before, @ticket_status)
    |> validate_inclusion(:status_after, @ticket_status)
    |> validate_inclusion(:location_type, @location_types)
  end

  @spec create(Ticket.t(), atom(), binary() | nil, Ecto.UUID.t() | nil, atom() | nil, map()) ::
          {:ok, TicketHistory.t()} | {:error, Ecto.Changeset.t()}
  def create(ticket, new_status, user_id \\ nil, location_id \\ nil, location_type \\ nil, metadata \\ %{}) do
    ticket
    |> build_create_changeset(new_status, user_id, location_id, location_type, metadata)
    |> Repo.insert()
  end

  @spec build_create_changeset(Ticket.t(), atom(), binary() | nil, Ecto.UUID.t() | nil, atom() | nil, map()) ::
          TicketHistory.t()
  def build_create_changeset(
        ticket,
        new_status,
        user_id \\ nil,
        location_id \\ nil,
        location_type \\ nil,
        metadata \\ %{}
      ) do
    actor_type = if is_nil(user_id), do: :SYSTEM, else: :USER
    inserted_at = if new_status == :USED, do: metadata[:check_in_date]
    # Since it was an offline scan, when :check_in_date has been set, it may be removed from the metadata
    # before inserting, to avoid saving duplicate data. Instead :sync_date is added to the map
    metadata = Map.delete(metadata, :check_in_date)

    # credo:disable-for-lines:1 Credo.Check.Design.TagTODO
    # TODO: clean up after accounts-service migration
    # remove _no_uuid path
    case Ecto.UUID.dump(user_id) do
      {:ok, _uuid} ->
        %TicketHistory{
          id: Ecto.UUID.generate(),
          ticket_id: ticket.id,
          actor_type: actor_type,
          actor_id: user_id,
          location_id: location_id,
          location_type: location_type,
          status_before: ticket.status,
          status_after: new_status,
          metadata: metadata,
          inserted_at: inserted_at
        }

      _no_uuid ->
        %TicketHistory{
          id: Ecto.UUID.generate(),
          ticket_id: ticket.id,
          actor_type: actor_type,
          actor_document_id: user_id,
          location_id: location_id,
          location_type: location_type,
          status_before: ticket.status,
          status_after: new_status,
          metadata: metadata,
          inserted_at: inserted_at
        }
    end
  end

  def get_all_for_ticket_id(ticket_id) do
    query =
      from(
        th in TicketHistory,
        where: th.ticket_id == ^ticket_id
      )

    Repo.all(query)
  end

  @doc """
  Gets the previous status of a ticket before it entered the REFUNDING state.

  This function looks through the ticket history to find the status the ticket
  had before it was set to REFUNDING.

  ## Parameters

  * `ticket_id` - The UUID of the ticket

  ## Returns

  * `status` - The previous status as an atom
  * `nil` - If no previous status is found
  """
  @spec get_previous_status_before_refunding(ticket_id :: Ecto.UUID.t()) :: atom() | nil
  def get_previous_status_before_refunding(ticket_id) do
    query =
      from(th in TicketHistory,
        where: th.ticket_id == ^ticket_id and th.status_after == :REFUNDING,
        select: th.status_before,
        order_by: [desc: th.inserted_at],
        limit: 1
      )

    Repo.one(query)
  end

  @spec build_delete_all_multi(Multi.t()) :: Multi.t()
  def build_delete_all_multi(multi) do
    Multi.delete_all(multi, :ticket_histories, fn %{tickets: {_, tickets}} ->
      ticket_ids = Enum.map(tickets, & &1.ticket_ids)
      from(th in __MODULE__, where: th.ticket_id in ^ticket_ids)
    end)
  end

  @spec location_types() :: [atom()]
  def location_types, do: @location_types
end
