defmodule OrdersService.Payments.Refund do
  @moduledoc false

  import Ecto.Query

  alias Adyen.Services.Checkout.Payments
  alias Ecto.Multi
  alias OrdersService.Order
  alias OrdersService.Refunds
  alias OrdersService.Refunds.RefundTransaction
  alias OrdersService.Refunds.RefundTransactionManager
  alias OrdersService.Refunds.RefundValidator
  alias OrdersService.Repo
  alias OrdersService.Ticket
  alias OrdersService.TicketGroup
  alias OrdersService.Util.Mailer

  require Logger

  # We use refunds for tickets as it supports partial refunds
  # thus we know that the merchant reference is the ticket id
  @spec finalize_successful_refund(item :: map()) :: :ok
  def finalize_successful_refund(%{"success" => "true", "merchantReference" => refund_transaction_id} = psp_response) do
    with {_, true} <- {:valid_merchant_reference, RefundValidator.valid_uuid?(refund_transaction_id)},
         {_, %RefundTransaction{refund_item_id: item_id, refund_item_type: refund_item_type} = refund_transaction}
         when refund_item_type in [:TICKET, :TICKET_GROUP] <-
           {:refund_transaction, Refunds.get_refund_transaction(refund_transaction_id)},
         {_, item} when not is_nil(item) <- {:item, get_item_by_id_and_type(item_id, refund_item_type)},
         {_, :ok} <- {:finalize_item_refund, Refunds.finalize_refund(item, refund_transaction, psp_response)} do
      Logger.debug("Refund finalized for ticket with merchantReference: #{inspect(item_id)}")
    else
      {:valid_merchant_reference, false} ->
        Logger.info("Invalid merchant reference for refund with merchantReference: #{inspect(refund_transaction_id)}")

      {:refund_transaction, _} ->
        Logger.info(
          "Could not find refund transaction for refund with merchantReference: #{inspect(refund_transaction_id)}"
        )

      {:item, _} ->
        Logger.error("Could not find item with refund id: #{inspect(refund_transaction_id)}")

      {:finalize_item_refund, {:error, error}} ->
        Logger.error(
          "Could not update item status with refund id: #{inspect(refund_transaction_id)} with error: #{inspect(error)}"
        )
    end

    :ok
  end

  # In case of a failed refund or reversal we receive the event code REFUND_FAILED or REFUNDED_REVERSED
  # we can't tell if it is an order or ticket refund so we rely on the refund item type
  @spec finalize_failed_refund(item :: map()) :: :ok
  def finalize_failed_refund(%{"success" => "true", "merchantReference" => refund_transaction_id} = psp_response) do
    with {_, true} <- {:valid_merchant_reference, RefundValidator.valid_uuid?(refund_transaction_id)},
         {_, %RefundTransaction{refund_item_id: _refund_item_id} = refund_transaction} <-
           {:refund_transaction, Refunds.get_refund_transaction(refund_transaction_id)},
         {_, {:ok, _}} <- {:revert_refund, Refunds.revert_refund(refund_transaction, psp_response)} do
      Logger.debug("Refund finalized for refund transaction with id: #{inspect(refund_transaction_id)}")
    else
      {:valid_merchant_reference, false} ->
        Logger.error("Invalid merchant reference for refund with id: #{inspect(refund_transaction_id)}")

      {:refund_transaction, _} ->
        Logger.error("Could not find refund transaction for refund with id: #{inspect(refund_transaction_id)}")

      {:revert_refund, error} ->
        Logger.error(
          "Could not update status for refund transaction with id: #{inspect(refund_transaction_id)} with error: #{inspect(error)}"
        )
    end

    :ok
  end

  # we use reversals instead of refunds for order since it ensures that the money is captured before refunding
  # in case of
  # thus we know that the merchant reference is a refund of type order
  @spec finalize_successful_reversal(item :: map()) :: :ok
  def finalize_successful_reversal(
        %{"success" => "true", "eventCode" => "CANCEL_OR_REFUND", "merchantReference" => refund_transaction_id} =
          psp_response
      ) do
    with {_, true} <-
           {:valid_merchant_reference, RefundValidator.valid_uuid?(refund_transaction_id)},
         {_, %RefundTransaction{refund_item_id: order_id, refund_item_type: :ORDER} = refund_transaction} <-
           {:refund_transaction, Refunds.get_refund_transaction(refund_transaction_id)},
         {_, %Order{} = order} <- {:order, Order.get(order_id)},
         {_, {:ok, _}} <-
           {:finalize_refund_transaction,
            RefundTransactionManager.finalize_refund_transaction(order, refund_transaction, psp_response)} do
      Logger.debug("Refund finalized for order with id: #{inspect(refund_transaction_id)}")
    else
      {:valid_merchant_reference, false} ->
        Logger.error("Invalid merchant reference for refund with id: #{inspect(refund_transaction_id)}")

      {:refund_transaction, _} ->
        Logger.error("Could not find refund transaction for refund with id: #{inspect(refund_transaction_id)}")

      {:order, _} ->
        Logger.error("Could not find order for refund with id: #{inspect(refund_transaction_id)}")

      {:finalize_refund_transaction, error} ->
        Logger.error(
          "Could not finalize refund transaction with id: #{inspect(refund_transaction_id)} with error: #{inspect(error)}"
        )
    end

    :ok
  end

  def init_order_refund(
        %{id: _order_id} = order,
        %{"pspReference" => psp_reference, "amount" => %{"value" => _value, "currency" => _currency}} = psp_response
      ) do
    Logger.info("Initiating refund for order with psp_reference: #{psp_reference}")

    refund_attrs = create_refund_transaction_payload(psp_response)

    with {_, {:ok, %{id: refund_transaction_id} = _refund_transaction}} <-
           {:refund_transaction, RefundTransactionManager.create_refund_transaction(refund_attrs)},
         {:refund, {:ok, _refund_res}} <- {:refund, Payments.reversals(refund_transaction_id, psp_reference)},
         {:status, {:ok, _order}} <- {:status, Order.set_status(order, :REFUND_PENDING)} do
      Logger.info("Refund initiated for order with psp_reference: #{psp_reference}")
      :ok
    else
      {:refund, {:error, msg}} ->
        Logger.critical("Refund failed for order with psp_reference: #{psp_reference} with error: #{inspect(msg)}")

        {:error, :init_refund_failed}

      {:status, {:error, msg}} ->
        Logger.critical(
          "Could not update order status for order with psp_reference: #{psp_reference} with error: #{inspect(msg)}"
        )

        {:error, :update_order_status_failed}
    end
  end

  @spec finalize(Order.t(), String.t(), integer(), String.t()) :: :ok | {:error, any()}
  def finalize(%{id: order_id} = order, psp_reference, amount, currency) do
    Logger.info(
      "Finalizing refund for order #{order_id} with refund psp_reference: #{psp_reference} for amount: #{amount} #{currency}"
    )

    order
    |> build_finalize_multi()
    |> Repo.transaction()
    |> case do
      {:ok, _res} ->
        handle_successful_refund(order, psp_reference, amount, currency)

      {:error, msg} ->
        Logger.error(
          "Could not update order status for order #{order_id} with refund psp_reference: #{psp_reference} with error: #{inspect(msg)}"
        )

        {:error, :update_order_status_failed}
    end
  end

  defp build_finalize_multi(%Order{id: order_id} = order) do
    now = DateTime.utc_now()

    Multi.new()
    |> Multi.update(:order, Order.changeset(order, %{status: :REFUNDED}))
    |> Multi.update_all(
      :tickets,
      fn _ ->
        from(t in Ticket,
          inner_join: ot in assoc(t, :order_ticket),
          where: ot.order_id == ^order_id,
          update: [set: [is_checkin_allowed: false, refunded_at: ^now]]
        )
      end,
      []
    )
  end

  defp handle_successful_refund(%{id: order_id} = _order, psp_reference, amount, currency) do
    Logger.info(
      "Refund finalized for order #{inspect(order_id)} with refund psp_reference: #{inspect(psp_reference)} for amount: #{inspect(psp_reference)} #{inspect(currency)}"
    )

    []
    |> handle_send_notify_adyen_refund_mail(order_id, psp_reference, amount, currency)
    |> case do
      [] -> :ok
      errors -> {:error, errors}
    end
  end

  defp handle_send_notify_adyen_refund_mail(errors, order_id, psp_reference, amount, currency) do
    order_id
    |> Mailer.send_notify_adyen_refund_mail(psp_reference, amount, currency)
    |> case do
      :ok ->
        Logger.info(
          "Sent refund email for order #{order_id} with refund psp_reference: #{psp_reference} for amount: #{amount} #{currency}"
        )

        errors

      {:error, msg} ->
        Logger.error(
          "Could not send refund email for order #{order_id} with refund psp_reference: #{psp_reference} for amount: #{amount} #{currency} with error: #{inspect(msg)}"
        )

        [:send_refund_email_failed | errors]
    end
  end

  defp create_refund_transaction_payload(
         %{
           "merchantReference" => reference,
           "paymentMethod" => payment_method,
           "pspReference" => psp_reference,
           "amount" => %{"value" => value, "currency" => currency}
         } = psp_response
       ) do
    %{
      status: :INITIATED,
      currency: currency,
      amount: value,
      refund_item_id: reference,
      refund_item_type: :ORDER,
      psp: "Adyen",
      psp_reference: psp_reference,
      psp_result: psp_response,
      payment_method: payment_method,
      reason: :TIMEDOUT,
      comment: "Refund as payment arrived after the order has timed out"
    }
  end

  defp get_item_by_id_and_type(item_id, :TICKET), do: Ticket.get(item_id, [:order_ticket])

  defp get_item_by_id_and_type(item_id, :TICKET_GROUP) do
    query =
      from(tg in TicketGroup,
        where: tg.id == ^item_id,
        preload: [tickets: [:order_ticket]]
      )

    Repo.one(query)
  end
end
