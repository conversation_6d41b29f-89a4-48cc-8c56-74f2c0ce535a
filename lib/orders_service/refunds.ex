defmodule OrdersService.Refunds do
  @moduledoc """
  The Refunds context.

  This module provides the main interface for refund operations,
  orchestrating the various components needed for processing refunds.
  """
  import Ecto.Query, warn: false

  alias OrdersService.Bill
  alias OrdersService.Order
  alias OrdersService.OrderTicket
  alias OrdersService.PersonalInformation
  alias OrdersService.Pubsub.Publisher.OrdersPublisher
  alias OrdersService.Refunds.RefundPayload
  alias OrdersService.Refunds.RefundPaymentProcessor
  alias OrdersService.Refunds.RefundTransaction
  alias OrdersService.Refunds.RefundTransactionManager
  alias OrdersService.Refunds.RefundTransactionPayload
  alias OrdersService.Repo
  alias OrdersService.Ticket, as: TicketDB
  alias OrdersService.TicketGroup
  alias OrdersService.Tickets.Ticket

  require Logger

  @doc """
  Processes refunds for multiple tickets with proper validation and group handling.

  This is the main entry point for refund processing, called from the controller
  after initial validations are complete.
  """
  @spec refund_tickets([TicketDB.t()], map(), user_id :: Ecto.UUID.t()) ::
          {:ok, [TicketDB.t()]} | {:error, any()}
  def refund_tickets(tickets, params, user_id) do
    params = Map.put(params, :user_id, user_id)

    with :ok <- process_ticket_refunds(tickets, params),
         :ok <- publish_order_updates(tickets) do
      {:ok, tickets}
    end
  end

  @doc """
  Reverts a refund transaction for webhook processing.

  This function is called when processing refund webhooks that indicate failure.
  It handles both order and ticket refund reversions.
  """
  @spec revert_refund(refund_transaction :: RefundTransaction.t(), psp_response :: map()) ::
          {:ok, TicketDB.t() | [TicketDB.t()] | Order.t()} | {:error, any()}
  def revert_refund(
        %RefundTransaction{refund_item_type: :ORDER, refund_item_id: refund_item_id} = refund_transaction,
        psp_response
      ) do
    case Order.get(refund_item_id) do
      nil ->
        Logger.error("Can't revert refund for order #{refund_item_id} because order was not found")
        {:error, :order_not_found}

      order ->
        RefundTransactionManager.revert_refund_transaction(order, refund_transaction, psp_response)
    end
  end

  def revert_refund(
        %RefundTransaction{refund_item_type: :TICKET, refund_item_id: refund_item_id} = refund_transaction,
        psp_response
      ) do
    with {_, %TicketDB{status: ticket_status} = ticket} <-
           {:get_ticket, TicketDB.get(refund_item_id, [:order_ticket])},
         {_, true} <-
           {:status_change_allowed, TicketDB.status_change_allowed?(ticket_status, :ACTIVE)},
         {_, {:ok, updated_ticket}} <-
           {:revert, RefundTransactionManager.revert_refund_transaction(ticket, refund_transaction, psp_response)},
         {_, :ok} <-
           {:revert_order, maybe_revert_order_status(ticket)} do
      Ticket.update_tickets_counter(updated_ticket)
      {:ok, updated_ticket}
    else
      {:get_ticket, nil} ->
        Logger.error("Can't revert refund for ticket #{refund_item_id} because ticket was not found")
        {:error, :ticket_not_found}

      {:status_change_allowed, false} ->
        Logger.error("Can't revert refund for ticket #{refund_item_id} because status change is not allowed")
        {:error, :invalid_status_change}

      {:revert, {:error, error}} ->
        Logger.critical("Can't revert refund for ticket #{refund_item_id} because of #{inspect(error)}")
        {:error, error}

      {:revert_order, {:error, error}} ->
        Logger.critical("Can't revert order status for ticket #{refund_item_id} because of #{inspect(error)}")
        {:error, error}
    end
  end

  def revert_refund(
        %RefundTransaction{refund_item_type: :TICKET_GROUP, refund_item_id: refund_item_id} = transaction,
        psp_response
      ) do
    with {_, %TicketGroup{tickets: tickets} = ticket_group} <- {:get_ticket_group, get_ticket_group(refund_item_id)},
         {_, true} <-
           {:status_change_allowed, tickets_status_change_allowed?(tickets, :ACTIVE)},
         {_, {:ok, [ticket | _] = updated_tickets}} <-
           {:revert, RefundTransactionManager.revert_refund_transaction(ticket_group, transaction, psp_response)},
         {_, :ok} <- {:revert_order, maybe_revert_order_status(ticket)} do
      Ticket.update_tickets_counter(updated_tickets)
      {:ok, updated_tickets}
    else
      {:get_ticket_group, nil} ->
        Logger.error("Can't revert refund for ticket group #{refund_item_id} because ticket group was not found")
        {:error, :ticket_group_not_found}

      {:status_change_allowed, false} ->
        Logger.error("Can't revert refund for ticket group #{refund_item_id} because status change is not allowed")
        {:error, :invalid_status_change}

      {:revert, {:error, error}} ->
        Logger.critical("Can't revert refund for ticket group #{refund_item_id} because of #{inspect(error)}")
        {:error, error}

      {:revert_order, {:error, error}} ->
        Logger.critical("Can't revert order status for ticket group #{refund_item_id} because of #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  Finalizes a ticket refund for webhook processing.

  This function is called when processing refund webhooks that indicate success.
  It updates the ticket status to REFUNDED and handles order status updates.
  """
  @spec finalize_refund(
          ticket :: TicketDB.t() | TicketGroup.t(),
          refund_transaction :: RefundTransaction.t(),
          psp_response :: map()
        ) ::
          :ok | {:error, any()}
  def finalize_refund(%TicketDB{} = ticket, refund_transaction, psp_response) do
    %TicketDB{id: ticket_id, status: current_status, order_ticket: %OrderTicket{order_id: order_id}} = ticket

    with {_, true} <- {:status_change_allowed, TicketDB.status_change_allowed?(current_status, :REFUNDED)},
         {_, {:ok, updated_ticket}} <-
           {:finalize_transaction,
            RefundTransactionManager.finalize_refund_transaction(ticket, refund_transaction, psp_response)},
         {_, :ok} <- {:finalize_order, maybe_finalize_order_status(order_id)} do
      Ticket.update_tickets_counter(updated_ticket)
      :ok
    else
      {:status_change_allowed, false} ->
        Logger.error("Can't finalize refund for ticket #{ticket_id} because status change is not allowed")
        {:error, :invalid_status_change}

      {:finalize_transaction, {:error, error}} ->
        Logger.critical("Can't finalize refund for ticket #{ticket_id} because of #{inspect(error)}")
        {:error, error}

      {:finalize_order, {:error, error}} ->
        Logger.critical("Can't finalize order for ticket #{ticket_id} because of #{inspect(error)}")
        {:error, error}
    end
  end

  def finalize_refund(%TicketGroup{} = ticket_group, refund_transaction, psp_response) do
    %TicketGroup{id: ticket_group_id, tickets: [%TicketDB{order_ticket: %OrderTicket{order_id: order_id}} | _]} =
      ticket_group

    with {_, true} <-
           {:status_change_allowed, tickets_status_change_allowed?(ticket_group.tickets, :REFUNDED)},
         {_, {:ok, updated_tickets}} <-
           {:finalize_transaction,
            RefundTransactionManager.finalize_refund_transaction(ticket_group, refund_transaction, psp_response)},
         {_, :ok} <- {:finalize_order, maybe_finalize_order_status(order_id)} do
      Ticket.update_tickets_counter(updated_tickets)
      :ok
    else
      {:status_change_allowed, false} ->
        Logger.error("Can't finalize refund for ticket group #{ticket_group_id} because status change is not allowed")
        {:error, :invalid_status_change}

      {:finalize_transaction, {:error, error}} ->
        Logger.critical("Can't finalize refund for ticket group #{ticket_group_id} because of #{inspect(error)}")
        {:error, error}

      {:finalize_order, {:error, error}} ->
        Logger.critical("Can't finalize order for ticket group #{ticket_group_id} because of #{inspect(error)}")
        {:error, error}
    end
  end

  @spec list_refund_transactions(params :: map()) :: Scrivener.Page.t()
  def list_refund_transactions(params) do
    query =
      from(rt in RefundTransaction,
        as: :refund_transaction,
        inner_join: t in TicketDB,
        as: :ticket,
        on: rt.refund_item_id == t.id and rt.refund_item_type == :TICKET,
        inner_join: pi in PersonalInformation,
        as: :personal_information,
        on: t.attendee_id == pi.id,
        inner_join: ot in OrderTicket,
        as: :order_ticket,
        on: ot.ticket_id == t.id,
        left_join: o in Order,
        as: :order,
        on: ot.order_id == o.id
      )

    query
    |> where(^filter_where_params(params))
    |> Repo.paginate(params)
  end

  @spec list_refund_transactions_by_ticket_and_ticket_group_ids(
          ticket_ids :: [Ecto.UUID.t()],
          ticket_group_ids :: [Ecto.UUID.t()]
        ) :: [map()]
  def list_refund_transactions_by_ticket_and_ticket_group_ids(ticket_ids, ticket_group_ids) do
    RefundTransaction
    |> join(:inner, [rt], t in TicketDB, on: rt.refund_item_id == t.id)
    |> join(:inner, [rt, t], pi in PersonalInformation, on: t.attendee_id == pi.id)
    |> where([rt, t, pi], t.id in ^ticket_ids or t.ticket_group_id in ^ticket_group_ids)
    |> select([rt, t, pi], %{refund: rt, ticket: t, ticket_personal_information: pi})
    |> Repo.all()
  end

  @doc """
  Gets a refund transaction by ID.
  """
  @spec get_refund_transaction(refund_transaction_id :: Ecto.UUID.t()) ::
          RefundTransaction.t() | nil
  def get_refund_transaction(refund_transaction_id) do
    RefundTransaction
    |> where([r], r.id == ^refund_transaction_id)
    |> Repo.one()
  end

  @doc """
  Gets a refund transaction by item ID and type.
  """
  @spec get_refund_transaction_by_type(
          refund_item_id :: Ecto.UUID.t(),
          refund_item_type :: atom()
        ) ::
          RefundTransaction.t() | nil
  def get_refund_transaction_by_type(refund_item_id, refund_item_type \\ :TICKET) do
    RefundTransaction
    |> where([r], r.refund_item_id == ^refund_item_id and r.refund_item_type == ^refund_item_type)
    |> Repo.one()
  end

  @spec process_ticket_refunds([TicketDB.t()], map()) :: :ok | {:error, any()}
  defp process_ticket_refunds(tickets, %{reason: reason} = params) do
    Logger.debug("Processing refunds for #{length(tickets)} tickets")

    with {:ok, payloads} <- build_refund_payloads(tickets, reason) do
      process_refund_payloads(payloads, params)
    end
  end

  @spec build_refund_payloads([TicketDB.t()], atom()) :: {:ok, [RefundPayload.t()]} | {:error, any()}
  defp build_refund_payloads(tickets, reason) do
    {grouped_tickets, individual_tickets} =
      Enum.split_with(tickets, fn ticket -> not is_nil(ticket.ticket_group_id) end)

    with {:ok, group_payloads} <- build_group_payloads(grouped_tickets, reason),
         {:ok, individual_payloads} <- build_individual_payloads(individual_tickets, reason) do
      {:ok, List.flatten([group_payloads | individual_payloads])}
    end
  end

  defp build_group_payloads(grouped_tickets, reason) do
    ticket_group_ids = grouped_tickets |> Enum.map(& &1.ticket_group_id) |> Enum.uniq()

    Enum.reduce_while(ticket_group_ids, {:ok, []}, fn ticket_group_id, {:ok, acc} ->
      case RefundPayload.build_for_ticket_group(ticket_group_id, reason) do
        {:ok, payload} -> {:cont, {:ok, [payload | acc]}}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end

  defp build_individual_payloads(individual_tickets, reason) do
    Enum.reduce_while(individual_tickets, {:ok, []}, fn ticket, {:ok, acc} ->
      case RefundPayload.build_for_ticket(ticket.id, reason) do
        {:ok, payload} -> {:cont, {:ok, [payload | acc]}}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end

  defp process_refund_payloads(payloads, params) do
    Enum.reduce_while(payloads, :ok, fn payload, _acc ->
      case process_refund_payload(payload, params) do
        :ok -> {:cont, :ok}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end

  # Process a single refund payload
  defp process_refund_payload(%RefundPayload{adyen_payload: nil, psp_reference: nil, item: item}, params) do
    process_direct_refund(item, params)
  end

  defp process_refund_payload(
         %RefundPayload{adyen_payload: adyen_payload, psp_reference: psp_reference, item: item},
         params
       )
       when not is_nil(adyen_payload) and not is_nil(psp_reference) do
    process_paid_refund(item, adyen_payload, psp_reference, params)
  end

  # Process direct refunds (no payment processing required)
  defp process_direct_refund(%TicketDB{} = ticket, %{user_id: user_id, reason: reason}) do
    transaction_payload = RefundTransactionPayload.build_for_item(0, ticket, user_id, reason)
    status_update_fn = fn -> update_ticket_to_refunded(ticket, user_id) end

    RefundPaymentProcessor.process_direct_refund(ticket, transaction_payload, status_update_fn)
  end

  defp process_direct_refund(%TicketGroup{} = ticket_group, %{user_id: user_id, reason: reason}) do
    %TicketGroup{tickets: tickets, bill: %Bill{total: total}} = ticket_group
    transaction_payload = RefundTransactionPayload.build_for_item(total, ticket_group, user_id, reason)
    status_update_fn = fn -> update_tickets_to_refunded(tickets, user_id) end

    RefundPaymentProcessor.process_direct_refund(ticket_group, transaction_payload, status_update_fn)
  end

  # Process paid refunds (require payment processing)
  defp process_paid_refund(%TicketDB{} = ticket, adyen_payload, psp_reference, %{user_id: user_id} = params) do
    transaction_payload =
      RefundTransactionPayload.build_adyen_payload(adyen_payload, user_id, ticket.id, :TICKET, psp_reference, params)

    status_update_fn = fn response -> update_ticket_to_refunding(ticket, user_id, response) end

    RefundPaymentProcessor.process_paid_refund(
      ticket,
      adyen_payload,
      psp_reference,
      transaction_payload,
      status_update_fn
    )
  end

  defp process_paid_refund(%TicketGroup{} = ticket_group, adyen_payload, psp_reference, %{user_id: user_id} = params) do
    %TicketGroup{id: ticket_group_id, tickets: tickets} = ticket_group

    transaction_payload =
      RefundTransactionPayload.build_adyen_payload(
        adyen_payload,
        user_id,
        ticket_group_id,
        :TICKET_GROUP,
        psp_reference,
        params
      )

    status_update_fn = fn response -> update_tickets_to_refunding(tickets, user_id, response) end

    RefundPaymentProcessor.process_paid_refund(
      ticket_group,
      adyen_payload,
      psp_reference,
      transaction_payload,
      status_update_fn
    )
  end

  defp update_ticket_to_refunded(%TicketDB{} = ticket, user_id) do
    with {:ok, _updated_ticket} <- TicketDB.set_status(ticket, :REFUNDED, user_id, nil, nil, true, %{}) do
      maybe_set_order_as_refunded(ticket.order_ticket.order_id)
    end
  end

  defp update_tickets_to_refunded(tickets, user_id) do
    with :ok <- update_all_tickets_to_refunded(tickets, user_id) do
      maybe_set_orders_as_refunded(tickets)
    end
  end

  defp update_all_tickets_to_refunded(tickets, user_id) do
    Enum.reduce_while(tickets, :ok, fn ticket, _acc ->
      case TicketDB.set_status(ticket, :REFUNDED, user_id, nil, nil, true, %{}) do
        {:ok, _updated_ticket} -> {:cont, :ok}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end

  defp update_ticket_to_refunding(%TicketDB{} = ticket, user_id, response) do
    ticket
    |> TicketDB.set_status(:REFUNDING, user_id, nil, nil, false, response)
    |> case do
      {:ok, _updated_ticket} -> :ok
      {:error, error} -> {:error, error}
    end
  end

  defp update_tickets_to_refunding(tickets, user_id, response) do
    Enum.reduce_while(tickets, :ok, fn ticket, _acc ->
      case TicketDB.set_status(ticket, :REFUNDING, user_id, nil, nil, false, response) do
        {:ok, _updated_ticket} -> {:cont, :ok}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end

  defp maybe_set_orders_as_refunded(tickets) do
    tickets
    |> Enum.map(& &1.order_ticket.order_id)
    |> Enum.uniq()
    |> Enum.reduce_while(:ok, fn order_id, _acc ->
      case maybe_set_order_as_refunded(order_id) do
        :ok -> {:cont, :ok}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end

  defp maybe_set_order_as_refunded(order_id) do
    order_id
    |> TicketDB.count_not_refunded_tickets_for_order_by_order_id()
    |> TicketDB.maybe_set_order_as_refunded(order_id)
  end

  defp publish_order_updates(tickets) do
    tickets
    |> extract_unique_order_ids()
    |> publish_updates_async()
    |> process_publish_results()
  end

  defp extract_unique_order_ids(tickets) do
    tickets
    |> Enum.map(& &1.order_ticket.order_id)
    |> Enum.uniq()
  end

  defp publish_updates_async(order_ids) do
    Task.async_stream(
      order_ids,
      &OrdersPublisher.publish_order_update/1,
      max_concurrency: 5,
      ordered: false
    )
  end

  defp process_publish_results(stream) do
    stream
    |> Stream.map(fn
      {:ok, :ok} -> :ok
      {:ok, {:error, _}} -> :error
      {:error, _} -> :error
    end)
    |> Enum.reduce_while(:ok, fn
      :ok, acc -> {:cont, acc}
      :error, _ -> {:halt, {:error, :order_update_publish_failed}}
    end)
  end

  defp maybe_revert_order_status(%TicketDB{order_ticket: %OrderTicket{order_id: order_id}}) do
    with {_, %Order{} = order} <- {:get_order, Order.get(order_id)},
         {_, :ok} <- {:revert_order, TicketDB.maybe_revert_order_status(order)} do
      :ok
    else
      {:get_order, nil} ->
        Logger.error("Can't revert order status for order #{order_id}: order not found")
        {:error, :order_not_found}

      {:revert_order, {:error, error}} ->
        Logger.error("Can't revert order status for order #{order_id}: #{inspect(error)}")
        {:error, error}
    end
  end

  defp maybe_finalize_order_status(order_id) do
    order_id
    |> TicketDB.count_not_refunded_tickets_for_order_by_order_id()
    |> TicketDB.maybe_set_order_as_refunded(order_id)
    |> case do
      :ok ->
        :ok

      {:error, error} ->
        Logger.error("Can't set order as refunded for order #{order_id}: #{inspect(error)}")
        {:error, error}
    end
  end

  defp filter_where_params(params) do
    Enum.reduce(params, dynamic(true), fn {key, value}, acc ->
      filter_where(acc, key, value)
    end)
  end

  defp filter_where(dynamic, :event_id, value) when not is_nil(value),
    do: dynamic([ticket: t], ^dynamic and t.event_id == ^value)

  defp filter_where(dynamic, :seller_id, value) when not is_nil(value),
    do: dynamic([order: o], ^dynamic and o.seller_id == ^value)

  defp filter_where(dynamic, :order_id, value) when not is_nil(value),
    do: dynamic([order_ticket: ot], ^dynamic and ot.order_id == ^value)

  defp filter_where(dynamic, :ticket_id, value) when not is_nil(value),
    do: dynamic([ticket: t], ^dynamic and t.id == ^value)

  defp filter_where(dynamic, _key, _value), do: dynamic

  defp get_ticket_group(refund_item_id) do
    query =
      from(tg in TicketGroup,
        where: tg.id == ^refund_item_id,
        preload: [tickets: [:order_ticket]]
      )

    Repo.one(query)
  end

  defp tickets_status_change_allowed?(tickets, new_status) do
    Enum.all?(tickets, fn ticket -> TicketDB.status_change_allowed?(ticket.status, new_status) end)
  end
end
