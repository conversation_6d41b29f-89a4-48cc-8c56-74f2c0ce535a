defmodule OrdersService.Refunds.RefundValidator do
  @moduledoc """
  Handles validation logic for refund operations.

  This module centralizes all validation logic related to refunds,
  making it easier to maintain and test validation rules.
  """

  alias OrdersService.Ticket
  alias OrdersService.Tickets.TicketValidator

  @doc """
  Validates that multiple tickets can be refunded.

  This function checks each ticket individually and returns an error
  if any ticket fails validation.
  """
  @spec validate_tickets_refundable([Ticket.t()]) :: :ok | {:error, any()}
  def validate_tickets_refundable(tickets) when is_list(tickets) do
    Enum.reduce_while(tickets, :ok, fn ticket, _acc ->
      case TicketValidator.validate_ticket_refundable(ticket) do
        :ok -> {:cont, :ok}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end

  @doc """
  Validates that a merchant reference is a valid UUID.

  This is used for webhook processing to ensure the reference
  can be used to look up refund transactions.
  """
  @spec valid_uuid?(String.t()) :: boolean()
  def valid_uuid?(merchant_reference) when is_binary(merchant_reference) do
    case Ecto.UUID.dump(merchant_reference) do
      {:ok, _} -> true
      _ -> false
    end
  end

  def valid_uuid?(_), do: false
end
