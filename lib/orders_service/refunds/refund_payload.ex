defmodule OrdersService.Refunds.RefundPayload do
  @moduledoc """
  Builds refund payloads for different types of tickets and ticket groups.

  This module determines whether a refund requires payment processing
  and builds the appropriate payload for Adyen or direct refunds.
  """

  import Ecto.Query

  alias Adyen.Services.BalancePlatform
  alias ExServiceClient.Services.EventsService
  alias OrdersService.Bill
  alias OrdersService.Config
  alias OrdersService.Order
  alias OrdersService.OrderTicket
  alias OrdersService.PayinTransaction
  alias OrdersService.Refunds.RefundSplitCalculator
  alias OrdersService.Repo
  alias OrdersService.Ticket
  alias OrdersService.TicketGroup
  alias OrdersService.TicketSwap.SwapTransaction

  require Logger

  @ticket_preloads [:swap_transaction, ticket_group: [:bill], order_ticket: [:bill, order: [:payin_transaction]]]
  @ticket_group_preloads [:bill, tickets: [:swap_transaction, order_ticket: [:bill, order: [:payin_transaction]]]]
  @currency "EUR"

  defstruct [:adyen_payload, :psp_reference, :item]

  @type t :: %__MODULE__{
          adyen_payload: map() | nil,
          psp_reference: String.t() | nil,
          item: Ticket.t() | TicketGroup.t() | Order.t()
        }

  @spec build_for_ticket(Ecto.UUID.t(), atom()) ::
          {:error, atom()}
          | {:ok, t()}
  def build_for_ticket(ticket_id, reason) when is_binary(ticket_id) do
    case Ticket.get(ticket_id, @ticket_preloads) do
      %Ticket{} = t -> build(t, reason)
      nil -> {:error, :ticket_not_found}
    end
  end

  @spec build_for_ticket_group(Ecto.UUID.t(), atom()) ::
          {:error, atom()}
          | {:ok, t()}
  def build_for_ticket_group(ticket_group_id, reason) when is_binary(ticket_group_id) do
    case get_ticket_group(ticket_group_id, @ticket_group_preloads) do
      %TicketGroup{} = tg -> build(tg, reason)
      nil -> {:error, :ticket_group_not_found}
    end
  end

  defp build(%Ticket{} = ticket, reason) do
    cond do
      swapped_ticket?(ticket) -> {:error, :swapped_ticket}
      refundable_ticket?(ticket) -> build_for_refundable_item(ticket, order_from_ticket(ticket), reason)
      true -> build_for_direct_item(ticket, reason)
    end
  end

  defp build(%TicketGroup{bill: %Bill{}, tickets: [%Ticket{} = ticket | _]} = ticket_group, reason) do
    cond do
      swapped_ticket?(ticket) -> {:error, :swapped_ticket_group}
      refundable_ticket?(ticket) -> build_for_refundable_item(ticket_group, order_from_ticket(ticket), reason)
      true -> build_for_direct_item(ticket_group, reason)
    end
  end

  @spec build_for_refundable_item(item :: Ticket.t() | TicketGroup.t(), order :: Order.t(), reason :: atom()) ::
          {:ok, t()} | {:error, any()}
  defp build_for_refundable_item(%{id: item_id, event_id: event_id} = item, order, reason) do
    %Order{payin_transaction: %PayinTransaction{psp_reference: psp_reference, payment_method: payment_method}} = order
    %Bill{total: bill_total} = bill = bill_from_item(item)

    with {:ok, balance_account_id} <- get_refund_balance_account(reason, event_id),
         true <- funds_available?(bill_total, balance_account_id) do
      config = Config.all_as_map()
      splits = RefundSplitCalculator.calculate_splits(bill, item, balance_account_id, config)

      adyen_payload = build_adyen_payload(bill_total, splits, payment_method)
      {:ok, %__MODULE__{adyen_payload: adyen_payload, psp_reference: psp_reference, item: item}}
    else
      {:error, reason} ->
        Logger.error("Refund balance account not found for event #{event_id} and reason #{reason}")
        {:error, :refund_balance_account_not_found}

      false ->
        Logger.error("Insufficient funds for refunding item #{item_id}")
        {:error, :insufficient_funds}
    end
  end

  defp build_adyen_payload(bill_total, splits, payment_method) do
    %{
      amount: %{value: bill_total, currency: @currency},
      reference: nil,
      splits: splits,
      payment_method: payment_method,
      merchant_refund_reason: "CUSTOMER REQUEST"
    }
  end

  defp get_refund_balance_account(reason, event_id) do
    case fetch_refund_balance_account(reason, event_id) do
      {:ok, balance_account_id} when not is_nil(balance_account_id) -> {:ok, balance_account_id}
      {:ok, nil} -> {:error, :balance_account_not_found}
      {:error, reason} -> {:error, reason}
    end
  end

  defp build_for_direct_item(item, _reason) do
    {:ok, %__MODULE__{adyen_payload: nil, psp_reference: nil, item: item}}
  end

  defp bill_from_item(%TicketGroup{bill: %Bill{} = bill}), do: bill
  defp bill_from_item(%Ticket{order_ticket: %OrderTicket{bill: %Bill{} = bill}}), do: bill

  defp fetch_refund_balance_account(:FAILURE_OR_GOODWILL_PROMOTER, event_id) do
    case EventsService.get_event_by_id(event_id) do
      {:ok, %{"balanceAccountId" => event_balance_account_id}} ->
        {:ok, event_balance_account_id}

      {:error, msg} ->
        Logger.error("Failed to get event balance account id for event #{event_id}: #{inspect(msg)}")
        {:error, :event_balance_account_id_not_found}
    end
  end

  defp fetch_refund_balance_account(:FAILURE_STAGEDATES, _) do
    case Config.all_as_map() do
      %{"stagedatesBalanceAccountId" => stagedates_balance_account_id} ->
        {:ok, stagedates_balance_account_id}

      _ ->
        Logger.error("Failed to get stagedates balance account id from config")
        {:error, :stagedates_balance_account_id_not_found}
    end
  end

  defp funds_available?(amount, balance_account_id) do
    case BalancePlatform.get_balance_account(balance_account_id) do
      {:ok, %{"balances" => [%{"balance" => balance}]}} ->
        balance >= amount

      {:error, _} ->
        Logger.error("Failed to check balance account balance for balance account id #{balance_account_id}")
        false
    end
  end

  defp swapped_ticket?(%Ticket{swap_transaction: %SwapTransaction{}}), do: true
  defp swapped_ticket?(_ticket), do: false

  defp refundable_ticket?(%Ticket{order_ticket: %OrderTicket{order: %Order{payin_transaction: %PayinTransaction{}}}}),
    do: true

  defp refundable_ticket?(_item), do: false

  defp order_from_ticket(%Ticket{order_ticket: %OrderTicket{order: %Order{} = order}}), do: order

  defp get_ticket_group(id, preloads) do
    query =
      from(tg in TicketGroup,
        where: tg.id == ^id,
        preload: ^preloads
      )

    Repo.one(query)
  end
end
