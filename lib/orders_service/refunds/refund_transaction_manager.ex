defmodule OrdersService.Refunds.RefundTransactionManager do
  @moduledoc """
  Manages database transactions for refund operations.

  This module handles all database-related operations for refunds,
  including creating and updating refund transactions and their history.
  """

  alias Ecto.Multi
  alias OrdersService.Order
  alias OrdersService.OrderHistory
  alias OrdersService.Refunds.RefundTransaction
  alias OrdersService.Repo
  alias OrdersService.Ticket, as: TicketDB
  alias OrdersService.TicketGroup
  alias OrdersService.TicketHistory
  alias OrdersService.TransactionHistory

  require Logger

  @doc """
  Creates a new refund transaction with history tracking.

  This function creates both the refund transaction record and its
  corresponding transaction history entry in a single database transaction.

  ## Parameters

  * `attrs` - The attributes for creating the refund transaction

  ## Returns

  * `{:ok, RefundTransaction.t()}` - If the refund transaction was created successfully
  * `{:error, {atom(), any()}}` - If there was an error creating the refund transaction
  """
  @spec create_refund_transaction(attrs :: map()) :: {:ok, RefundTransaction.t()} | {:error, {atom(), any()}}
  def create_refund_transaction(attrs) do
    Multi.new()
    |> Multi.insert(:insert_refund_transaction, RefundTransaction.changeset(%RefundTransaction{}, attrs))
    |> Multi.insert(:insert_transaction_history, fn %{insert_refund_transaction: refund_transaction} ->
      TransactionHistory.changeset(%TransactionHistory{}, %{
        state: Atom.to_string(refund_transaction.status),
        transaction_id: refund_transaction.id
      })
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{insert_refund_transaction: refund_transaction}} -> {:ok, refund_transaction}
      {:error, operation, failed_value, _changes_so_far} -> {:error, {operation, failed_value}}
    end
  end

  @doc """
  Updates a refund transaction with the PSP response.
  """
  @spec update_transaction_with_response(
          refund_transaction :: RefundTransaction.t(),
          psp_response :: map()
        ) :: {:ok, RefundTransaction.t()} | {:error, Ecto.Changeset.t()}
  def update_transaction_with_response(%RefundTransaction{} = refund_transaction, psp_response) do
    refund_transaction
    |> RefundTransaction.changeset(%{psp_result: psp_response})
    |> Repo.update()
  end

  @doc """
  Updates a refund transaction to failed status with error information.
  """
  @spec update_transaction_to_failed(
          refund_transaction :: RefundTransaction.t(),
          error :: any()
        ) :: {:ok, RefundTransaction.t()} | {:error, Ecto.Changeset.t()}
  def update_transaction_to_failed(%RefundTransaction{} = refund_transaction, error) do
    refund_transaction
    |> RefundTransaction.changeset(%{
      status: :FAILED,
      psp_result: %{error: inspect(error)}
    })
    |> Repo.update()
  end

  @doc """
  Finalizes a refund transaction for any refundable entity (ticket, ticket group, or order).

  This unified function handles the finalization process by:
  1. Updating the entity status to refunded
  2. Creating appropriate history records
  3. Updating the refund transaction status

  ## Parameters

  * `entity` - The entity to refund (TicketDB.t(), TicketGroup.t(), or Order.t())
  * `refund_transaction` - The refund transaction to finalize
  * `psp_response` - The PSP response data

  ## Returns

  * `{:ok, entity}` - The updated entity (or list of tickets for ticket groups)
  * `{:error, {atom(), any()}}` - Error tuple with operation and failed value
  """
  @spec finalize_refund_transaction(
          entity :: TicketDB.t() | TicketGroup.t() | Order.t(),
          refund_transaction :: RefundTransaction.t(),
          psp_response :: map()
        ) :: {:ok, TicketDB.t() | [TicketDB.t()] | Order.t()} | {:error, {atom(), any()}}
  def finalize_refund_transaction(entity, %RefundTransaction{} = refund_transaction, psp_response) do
    Multi.new()
    |> add_entity_update(entity, refund_transaction.actor_id, :REFUNDED)
    |> add_shared_operations(refund_transaction, :COMPLETED, "REFUNDED", psp_response)
    |> Repo.transaction()
    |> handle_transaction_result(entity)
  end

  @doc """
  Reverts a refund transaction for any refundable entity (ticket, ticket group, or order).

  This unified function handles the revert process by:
  1. Updating the entity status back to its previous status before refunding
  2. Creating appropriate history records
  3. Updating the refund transaction status to failed

  ## Parameters

  * `entity` - The entity to revert (TicketDB.t(), TicketGroup.t(), or Order.t())
  * `refund_transaction` - The refund transaction to revert
  * `psp_response` - The PSP response data

  ## Returns

  * `{:ok, entity}` - The updated entity (or list of tickets for ticket groups)
  * `{:error, {atom(), any()}}` - Error tuple with operation and failed value
  """
  @spec revert_refund_transaction(
          entity :: TicketDB.t() | TicketGroup.t() | Order.t(),
          refund_transaction :: RefundTransaction.t(),
          psp_response :: map()
        ) :: {:ok, TicketDB.t() | [TicketDB.t()] | Order.t()} | {:error, {atom(), any()}}
  def revert_refund_transaction(entity, %RefundTransaction{} = refund_transaction, psp_response) do
    Multi.new()
    |> add_entity_revert_update(entity, refund_transaction.actor_id)
    |> add_shared_operations(refund_transaction, :FAILED, "FAILED", psp_response)
    |> Repo.transaction()
    |> handle_transaction_result(entity)
  end

  # Handle ticket updates
  defp add_entity_update(multi, %TicketDB{} = ticket, actor_id, status) do
    Multi.run(multi, :update_entity, fn _repo, _changes ->
      TicketDB.set_status(ticket, status, actor_id, nil, nil, true, %{})
    end)
  end

  # Handle ticket group updates
  defp add_entity_update(multi, %TicketGroup{tickets: tickets}, actor_id, status) do
    ticket_payloads =
      Enum.map(tickets, fn ticket ->
        %{ticket: ticket, metadata: %{}, location_id: nil, location_type: nil}
      end)

    Multi.run(multi, :update_entity, fn _repo, _changes ->
      case TicketDB.set_statuses(ticket_payloads, status, actor_id, true) do
        {updated_tickets, []} -> {:ok, updated_tickets}
        {_, errors} -> {:error, errors}
      end
    end)
  end

  # Handle order updates for finalization
  defp add_entity_update(multi, %Order{} = order, _actor_id, status) do
    multi
    |> Multi.update(:update_entity, Order.changeset(order, %{status: status}))
    |> Multi.run(:insert_order_history, fn _repo, _changes ->
      OrderHistory.create(order, status)
    end)
  end

  # Handle ticket revert updates using history
  defp add_entity_revert_update(multi, %TicketDB{} = ticket, actor_id) do
    Multi.run(multi, :update_entity, fn _repo, _changes ->
      previous_status = get_previous_ticket_status(ticket.id)

      TicketDB.set_status(ticket, previous_status, actor_id, nil, nil, true, %{})
    end)
  end

  # Handle ticket group revert updates using history
  defp add_entity_revert_update(multi, %TicketGroup{tickets: tickets}, actor_id) do
    Multi.run(multi, :update_entity, fn _repo, _changes ->
      Enum.reduce_while(tickets, {:ok, []}, fn ticket, {:ok, updated_tickets} ->
        revert_single_ticket(ticket, actor_id, updated_tickets)
      end)
    end)
  end

  # Handle order revert updates using history
  defp add_entity_revert_update(multi, %Order{} = order, _actor_id) do
    previous_status = get_previous_order_status(order.id)

    multi
    |> Multi.update(:update_entity, Order.changeset(order, %{status: previous_status}))
    |> Multi.run(:insert_order_history, fn _repo, _changes ->
      OrderHistory.create(order, previous_status)
    end)
  end

  defp add_shared_operations(multi, refund_transaction, transaction_status, history_state, psp_response) do
    multi
    |> Multi.insert(
      :insert_transaction_history,
      TransactionHistory.changeset(%TransactionHistory{}, %{
        state: history_state,
        transaction_id: refund_transaction.id
      })
    )
    |> Multi.update(
      :update_refund_transaction,
      RefundTransaction.changeset(refund_transaction, %{
        status: transaction_status,
        psp_result: psp_response
      })
    )
  end

  defp handle_transaction_result({:ok, %{update_entity: updated_entity}}, %TicketDB{}), do: {:ok, updated_entity}
  defp handle_transaction_result({:ok, %{update_entity: updated_order}}, %Order{}), do: {:ok, updated_order}

  defp handle_transaction_result({:ok, result}, %TicketGroup{}) do
    updated_tickets =
      Enum.flat_map(result, fn
        {:update_entity, updated_tickets} -> updated_tickets
        _ -> []
      end)

    {:ok, updated_tickets}
  end

  defp handle_transaction_result({:error, operation, failed_value, _changes_so_far}, _entity) do
    {:error, {operation, failed_value}}
  end

  defp get_previous_ticket_status(ticket_id, fallback \\ :ACTIVE) do
    case TicketHistory.get_previous_status_before_refunding(ticket_id) do
      nil ->
        Logger.warning("No previous status found for ticket #{ticket_id}, falling back to #{fallback}")
        fallback

      status ->
        status
    end
  end

  defp get_previous_order_status(order_id, fallback \\ :PAID) do
    case OrderHistory.get_previous_status_before_refunding(order_id) do
      nil ->
        Logger.warning("No previous status found for order #{order_id}, falling back to #{fallback}")
        fallback

      status ->
        status
    end
  end

  defp revert_single_ticket(ticket, actor_id, updated_tickets) do
    previous_status = get_previous_ticket_status(ticket.id)

    case TicketDB.set_status(ticket, previous_status, actor_id, nil, nil, true, %{}) do
      {:ok, updated_ticket} -> {:cont, {:ok, [updated_ticket | updated_tickets]}}
      error -> {:halt, error}
    end
  end
end
