defmodule OrdersService.Refunds.RefundSplitCalculator do
  @moduledoc """
  Handles calculation of refund splits for different amount types (fees, kickbacks, etc).

  This module provides a clean, reusable way to calculate how refunds should be
  split across different balance accounts based on the original bill structure.
  """

  alias OrdersService.Bill
  alias OrdersService.Ticket, as: TicketDB
  alias OrdersService.TicketGroup

  require Logger

  @currency "EUR"

  @type split_item :: %{
          String.t() => String.t() | map()
        }

  @split_configs [
    %{
      type: "presale",
      amount_fields: [:presale_fee, :presale_fee_tax],
      config_key: "presaleFeeAdyenBalanceAccountId",
      use_promoter_account: false
    },
    %{
      type: "transaction",
      amount_fields: [:system_fee, :system_fee_tax],
      config_key: "transactionFeeAdyenBalanceAccountId",
      use_promoter_account: false
    },
    %{
      type: "kickback",
      amount_fields: [:promoter_kickback, :promoter_kickback_tax],
      config_key: nil,
      use_promoter_account: true
    },
    %{
      type: "promoter",
      amount_fields: [:promoter_total],
      config_key: nil,
      use_promoter_account: true
    },
    %{
      type: "donation",
      amount_fields: [:donation],
      config_key: "defaultDonationBalanceAccountId",
      use_promoter_account: false
    },
    %{
      type: "future_demand",
      amount_fields: [:future_demand_fee, :future_demand_fee_tax],
      config_key: "futureDemandFeeAdyenBalanceAccountId",
      use_promoter_account: false
    },
    %{
      type: "payment_method_fee",
      amount_fields: [:payment_method_fee, :payment_method_fee_tax],
      config_key: "paymentMethodFeeAdyenBalanceAccountId",
      use_promoter_account: false
    }
  ]

  @doc """
  Calculates refund splits based on bill amounts and configuration. Amounts can be fees, kickbacks, etc.

  Returns a list of split items for Adyen refund processing.
  Each split represents a portion of the refund that should go to a specific balance account.
  """
  @spec calculate_splits(
          bill :: Bill.t(),
          item :: TicketDB.t() | TicketGroup.t(),
          balance_account_id :: String.t(),
          config :: map()
        ) :: [split_item()]
  def calculate_splits(%Bill{} = bill, item, balance_account_id, config) do
    reference = reference_from_item(item)

    Enum.reduce(@split_configs, [], fn fee_config, acc ->
      add_split(acc, bill, fee_config, reference, balance_account_id, config)
    end)
  end

  defp add_split(splits, bill, fee_config, reference, balance_account_id, config) do
    %{
      type: type,
      amount_fields: amount_fields,
      config_key: config_key,
      use_promoter_account: use_promoter_account
    } = fee_config

    amount = calculate_total_amount(bill, amount_fields)
    account_id = get_account_id(config_key, balance_account_id, use_promoter_account, config)

    case {amount > 0, account_id} do
      {_amount, nil} ->
        Logger.critical("Can't add refund split for #{type} because account ID is nil")
        splits

      {true, account_id} ->
        split = build_split(type, amount, account_id, reference)
        [split | splits]

      _ ->
        splits
    end
  end

  defp calculate_total_amount(bill, amount_fields) do
    amount_fields
    |> Enum.map(&Map.get(bill, &1))
    |> Enum.reject(&is_nil/1)
    |> Enum.sum()
  end

  defp get_account_id(_config_key, balance_account_id, true, _config), do: balance_account_id
  defp get_account_id(config_key, _balance_account_id, false, config), do: config[config_key]

  defp build_split(type, amount, account_id, reference) do
    %{
      "type" => "Commission",
      "account" => account_id,
      "reference" => "#{type}-#{reference}",
      "amount" => %{
        "currency" => @currency,
        "value" => amount
      }
    }
  end

  defp reference_from_item(%TicketDB{id: ticket_id}), do: ticket_id
  defp reference_from_item(%TicketGroup{id: ticket_group_id}), do: ticket_group_id
end
