defmodule OrdersService.Refunds.RefundPaymentProcessor do
  @moduledoc """
  Handles payment processing for refunds through Adyen.

  This module encapsulates all Adyen-specific payment processing logic,
  making it easier to test and maintain payment-related functionality.
  """

  alias Adyen.Services.Checkout
  alias OrdersService.Refunds.RefundTransactionManager

  require Logger

  @doc """
  Processes a refund through Adyen for a single item.

  This function handles the complete flow of processing a paid refund:
  1. Creates a refund transaction record
  2. Calls Adyen to process the refund
  3. Updates the transaction with the response
  4. Updates the item status

  ## Parameters

  * `item` - The item to refund (e.g., ticket, ticket group, order)
  * `adyen_payload` - The payload to send to <PERSON>yen for the refund
  * `psp_reference` - The PSP reference for the original payment
  * `transaction_payload` - The payload for creating the refund transaction
  * `status_update_fn` - A function to update the item status after the refund is processed

  ## Returns

  * `:ok` - If the refund was processed successfully
  * `{:error, any()}` - If any step of the process failed
  """
  @spec process_paid_refund(
          item :: struct(),
          adyen_payload :: map(),
          psp_reference :: String.t(),
          transaction_payload :: map(),
          status_update_fn :: function()
        ) :: :ok | {:error, any()}
  def process_paid_refund(item, adyen_payload, psp_reference, transaction_payload, status_update_fn) do
    item_id = get_item_id(item)
    item_type = get_item_type_name(item)

    Logger.debug("Processing paid refund for #{item_type} #{item_id} with payload #{inspect(adyen_payload)}")

    with {_, {:ok, refund_transaction}} <-
           {:create_refund_transaction, RefundTransactionManager.create_refund_transaction(transaction_payload)},
         updated_payload = Map.put(adyen_payload, :reference, refund_transaction.id),
         {_, {:ok, response}} <- {:refund, Checkout.refunds(updated_payload, psp_reference)},
         {_, {:ok, _updated_transaction}} <-
           {:update_transaction,
            RefundTransactionManager.update_transaction_with_response(refund_transaction, response)},
         {_, :ok} <- {:status_update, status_update_fn.(response)} do
      Logger.debug("Successfully processed paid refund for #{item_type} #{item_id}")
      :ok
    else
      {:create_refund_transaction, {:error, {operation, failed_value}}} ->
        Logger.error(
          "Failed to create refund transaction in step #{inspect(operation)} for #{item_type} #{item_id}: #{inspect(failed_value)}"
        )

        {:error, failed_value}

      {:refund, {:error, refund_error}} ->
        Logger.error("Adyen refund failed for #{item_type} #{item_id}: #{inspect(refund_error)}")
        {:error, refund_error}

      {:update_transaction, {:error, update_error}} ->
        Logger.error("Failed to update refund transaction for #{item_type} #{item_id}: #{inspect(update_error)}")
        {:error, update_error}

      {:status_update, error} ->
        Logger.error("Failed to update item status for #{item_type} #{item_id}: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  Processes a direct refund (no payment processing required).

  This is used for guest list tickets, seller tickets, or other items
  that don't require actual payment processing.

  ## Parameters

  * `item` - The item to refund (e.g., ticket, ticket group, order)
  * `transaction_payload` - The payload for creating the refund transaction
  * `status_update_fn` - A function to update the item status after the refund is processed

  ## Returns

  * `:ok` - If the refund was processed successfully
  * `{:error, any()}` - If any step of the process failed
  """
  @spec process_direct_refund(
          item :: struct(),
          transaction_payload :: map(),
          status_update_fn :: function()
        ) :: :ok | {:error, any()}
  def process_direct_refund(item, transaction_payload, status_update_fn) do
    item_id = get_item_id(item)
    item_type = get_item_type_name(item)

    Logger.debug("Processing direct refund for #{item_type} #{item_id}")

    with {:ok, _refund_transaction} <- RefundTransactionManager.create_refund_transaction(transaction_payload),
         :ok <- status_update_fn.() do
      Logger.debug("Successfully processed direct refund for #{item_type} #{item_id}")
      :ok
    else
      {:error, {operation, failed_value}} ->
        Logger.error(
          "Failed to create refund transaction in step #{inspect(operation)} for #{item_type} #{item_id}: #{inspect(failed_value)}"
        )

        {:error, failed_value}

      error ->
        Logger.error("Failed to process direct refund for #{item_type} #{item_id}: #{inspect(error)}")
        {:error, error}
    end
  end

  defp get_item_id(%{id: id}), do: id

  defp get_item_type_name(%OrdersService.Ticket{}), do: "ticket"
  defp get_item_type_name(%OrdersService.TicketGroup{}), do: "ticket_group"
  defp get_item_type_name(_), do: "unknown"
end
