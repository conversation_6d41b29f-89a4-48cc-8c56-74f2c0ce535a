defmodule OrdersService.OrderHistory do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query

  alias Ecto.Multi
  alias OrdersService.OrderHistory
  alias OrdersService.Repo

  @order_status [
    :CREATED,
    :FAILED,
    :PENDING,
    :PAID,
    :REFUND_PENDING,
    :REFUNDED,
    :TIMEDOUT,
    :AWAITING_PAYMENT,
    :DEFRAUDED,
    :INVITATION_REJECTED
  ]

  @actor_types [
    :USER,
    :SYSTEM
  ]

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  @schema_prefix "orders"
  schema "order_histories" do
    field :metadata, :map
    field :order_id, Ecto.UUID
    field :actor_id, Ecto.UUID
    field :actor_document_id, :string
    field :actor_type, Ecto.Enum, values: @actor_types
    field :status_before, Ecto.Enum, values: @order_status
    field :status_after, Ecto.Enum, values: @order_status

    timestamps(updated_at: false)
  end

  # TODO: remove after refactor order process. It's a workaround to handle the history in Ecto Multis
  def changeset(order_history, attrs) do
    order_history
    |> cast(attrs, [
      :order_id,
      :actor_type,
      :status_before,
      :status_after,
      :inserted_at,
      :actor_id,
      :actor_document_id
    ])
    |> validate_required([
      :order_id,
      :actor_type,
      :status_before,
      :status_after,
      :inserted_at
    ])
  end

  def create(order, new_status, user_id \\ nil) do
    actor_type = if is_nil(user_id), do: :USER, else: :SYSTEM

    # TODO: clean up after accounts-service migration
    # remove _no_uuid path
    order_history =
      case Ecto.UUID.dump(user_id) do
        {:ok, _uuid} ->
          %OrderHistory{
            id: Ecto.UUID.generate(),
            order_id: order.id,
            actor_type: actor_type,
            actor_id: user_id,
            status_before: order.status,
            status_after: new_status
          }

        _no_uuid ->
          %OrderHistory{
            id: Ecto.UUID.generate(),
            order_id: order.id,
            actor_type: actor_type,
            actor_document_id: user_id,
            status_before: order.status,
            status_after: new_status
          }
      end

    Repo.insert(order_history)
  end

  @doc """
  Gets the previous status of an order before it entered the REFUNDING state.

  This function looks through the order history to find the status the order
  had before it was set to REFUND_PENDING (which is the order equivalent of REFUNDING).

  ## Parameters

  * `order_id` - The UUID of the order

  ## Returns

  * `status` - The previous status as an atom
  * `nil` - If no previous status is found
  """
  @spec get_previous_status_before_refunding(order_id :: Ecto.UUID.t()) :: atom() | nil
  def get_previous_status_before_refunding(order_id) do
    query =
      from(oh in OrderHistory,
        where: oh.order_id == ^order_id and oh.status_after == :REFUND_PENDING,
        select: oh.status_before,
        order_by: [desc: oh.inserted_at],
        limit: 1
      )

    Repo.one(query)
  end

  @spec build_delete_all_multi(Multi.t()) :: Multi.t()
  def build_delete_all_multi(multi) do
    Multi.delete_all(multi, :order_histories, fn %{order_tickets: {_, order_tickets}} ->
      order_ids = Enum.map(order_tickets, & &1.order_ids)
      from(oh in __MODULE__, where: oh.order_id in ^order_ids)
    end)
  end
end
