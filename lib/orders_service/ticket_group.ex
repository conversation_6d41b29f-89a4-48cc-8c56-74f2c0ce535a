defmodule OrdersService.TicketGroup do
  @moduledoc """
  TicketGroup schema and changeset
  """

  use Ecto.Schema

  import Ecto.Changeset

  alias OrdersService.Bill
  alias OrdersService.Ticket
  # styler:sort
  @type t :: %__MODULE__{
          amount: pos_integer(),
          bill: Bill.t() | nil | Ecto.Association.NotLoaded.t(),
          bill_id: Ecto.UUID.t(),
          category_id: Ecto.UUID.t(),
          deleted_at: DateTime.t() | nil,
          event_id: Ecto.UUID.t(),
          id: Ecto.UUID.t(),
          inserted_at: DateTime.t(),
          label: String.t(),
          tickets: [Ticket.t()],
          type: atom(),
          updated_at: DateTime.t(),
          variant_id: Ecto.UUID.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  @schema_prefix "orders"
  # styler:sort
  schema "ticket_groups" do
    belongs_to(:bill, Bill)

    field(:amount, :integer)
    field(:category_id, :binary_id)
    field(:deleted_at, :utc_datetime)
    field(:event_id, :binary_id)
    field(:label, :string)
    field(:type, Ecto.Enum, values: [:TABLE, :BOOTH])
    field(:variant_id, :binary_id)

    has_many(:tickets, Ticket)

    timestamps()
  end

  def changeset(ticket, attrs) do
    ticket
    |> cast(attrs, [
      :event_id,
      :variant_id,
      :category_id,
      :amount,
      :bill_id,
      :type,
      :label,
      :deleted_at
    ])
    |> validate_required([
      :event_id,
      :category_id,
      :amount,
      :bill_id,
      :type,
      :label
    ])
    |> validate_inclusion(:type, [:TABLE, :BOOTH])
  end
end
