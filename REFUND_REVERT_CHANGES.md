# Refund Transaction Revert Status Changes

## Overview

This document describes the changes made to improve the refund transaction reverting logic in the OrdersService. Previously, when reverting a refund transaction, the system would set orders and tickets to static statuses (`:TIMEDOUT` for orders, `:ACTIVE` for tickets). Now, the system uses OrderHistory and TicketHistory to restore entities to their previous status before entering the refunding state.

## Changes Made

### 1. OrderHistory Module (`lib/orders_service/order_history.ex`)

Added a new function `get_previous_status_before_refunding/1`:

```elixir
@spec get_previous_status_before_refunding(order_id :: Ecto.UUID.t()) ::
        {:ok, atom()} | {:error, :no_previous_status}
def get_previous_status_before_refunding(order_id)
```

This function queries the order history to find the status an order had before it was set to `:REFUND_PENDING` (the order equivalent of the refunding state).

### 2. TicketHistory Module (`lib/orders_service/ticket_history.ex`)

Added a new function `get_previous_status_before_refunding/1`:

```elixir
@spec get_previous_status_before_refunding(ticket_id :: Ecto.UUID.t()) ::
        {:ok, atom()} | {:error, :no_previous_status}
def get_previous_status_before_refunding(ticket_id)
```

This function queries the ticket history to find the status a ticket had before it was set to `:REFUNDING`.

### 3. RefundTransactionManager Module (`lib/orders_service/refunds/transaction_manager.ex`)

#### Modified `revert_refund_transaction/3`

- Updated the function to call `add_entity_revert_update/2` instead of `add_entity_update/3` with hardcoded `:ACTIVE`
- Updated documentation to reflect that entities are restored to their previous status

#### Added `add_entity_revert_update/2` functions

Created new private functions to handle reverting entities using history:

- **For tickets**: Uses `TicketHistory.get_previous_status_before_refunding/1` to get the previous status, falls back to `:ACTIVE` if no history is found
- **For ticket groups**: Processes each ticket individually, getting their previous statuses from history
- **For orders**: Uses `OrderHistory.get_previous_status_before_refunding/1` to get the previous status, falls back to `:TIMEDOUT` if no history is found

## Behavior Changes

### Before

- **Ticket revert**: Always set to `:ACTIVE`
- **Order revert**: Always set to `:TIMEDOUT`

### After

- **Ticket revert**: Restored to the status it had before `:REFUNDING`, with `:ACTIVE` as fallback
- **Order revert**: Restored to the status it had before `:REFUND_PENDING`, with `:TIMEDOUT` as fallback

## Examples

### Ticket Status Flow

1. Ticket starts as `:ACTIVE`
2. Ticket is set to `:REFUNDING` (history entry created: `:ACTIVE` → `:REFUNDING`)
3. Refund fails and needs to be reverted
4. **Old behavior**: Ticket set to `:ACTIVE` (hardcoded)
5. **New behavior**: Ticket restored to `:ACTIVE` (from history)

### Order Status Flow

1. Order starts as `:PAID`
2. Order is set to `:REFUND_PENDING` (history entry created: `:PAID` → `:REFUND_PENDING`)
3. Refund fails and needs to be reverted
4. **Old behavior**: Order set to `:TIMEDOUT` (hardcoded)
5. **New behavior**: Order restored to `:PAID` (from history)

## Testing

Added comprehensive tests in `test/orders_service/refunds_test.exs`:

- Tests for `TicketHistory.get_previous_status_before_refunding/1`
- Tests for `OrderHistory.get_previous_status_before_refunding/1`
- Integration tests for `revert_refund_transaction/3` with both tickets and orders
- Tests for fallback behavior when no history exists

## Backward Compatibility

The changes are backward compatible:

- If no history exists, the system falls back to the previous hardcoded behavior
- Existing refund transactions continue to work as before
- The API surface remains unchanged

## Benefits

1. **More accurate status restoration**: Entities are restored to their actual previous state
2. **Better audit trail**: The system respects the historical state changes
3. **Improved data integrity**: Reduces the risk of incorrect status assignments
4. **Flexible fallback**: Maintains the previous behavior when history is unavailable
