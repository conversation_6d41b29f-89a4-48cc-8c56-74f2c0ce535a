defmodule OrdersService.RefundsTest do
  use OrdersService.DataCase

  alias OrdersService.Bill
  alias OrdersService.Config
  alias OrdersService.Factory
  alias OrdersService.Order
  alias OrdersService.OrderHistory
  alias OrdersService.Refunds
  alias OrdersService.Refunds.RefundSplitCalculator
  alias OrdersService.Refunds.RefundTransaction
  alias OrdersService.Refunds.RefundTransactionManager
  alias OrdersService.Refunds.RefundValidator
  alias OrdersService.Ticket, as: TicketDB
  alias OrdersService.TicketHistory

  describe "refund_transactions" do
    import OrdersService.RefundsFixtures

    @invalid_attrs %{
      status: nil,
      currency: nil,
      psp_reference: nil,
      amount: nil,
      payment_method: nil,
      refund_item_id: nil,
      psp: nil,
      psp_result: nil
    }

    @currency "EUR"

    # Sample balance account IDs for testing
    @balance_account_id "*************************"

    # Example Test Case 1: All values are zero
    test "returns an empty list when all fees and donations are zero" do
      invitation_bill = Factory.build(:invitation_bill)
      ticket = Factory.build(:ticket)
      config = Config.all_as_map()
      assert RefundSplitCalculator.calculate_splits(invitation_bill, ticket, @balance_account_id, config) == []
    end

    # Example Test Case 3: All fees and donations are non-zero
    test "returns a split for each non-zero fee and donation" do
      %Bill{
        donation: donation,
        presale_fee: presale_fee,
        presale_fee_tax: presale_fee_tax,
        promoter_total: promoter_total,
        future_demand_fee: future_demand_fee,
        future_demand_fee_tax: future_demand_fee_tax,
        system_fee: system_fee,
        system_fee_tax: system_fee_tax,
        promoter_kickback: promoter_kickback,
        promoter_kickback_tax: promoter_kickback_tax
      } = bill = Factory.build(:bill)

      %TicketDB{id: reference} = ticket = Factory.build(:ticket)

      %{
        "presaleFeeAdyenBalanceAccountId" => presale_fee_adyen_balance_account_id,
        "transactionFeeAdyenBalanceAccountId" => transaction_fee_adyen_balance_account_id,
        "defaultDonationBalanceAccountId" => default_donation_balance_account_id,
        "futureDemandFeeAdyenBalanceAccountId" => future_demand_fee_adyen_balance_account_id
      } = config = Config.all_as_map()

      split_attributes = [
        {"presale", presale_fee + presale_fee_tax, presale_fee_adyen_balance_account_id},
        {"transaction", system_fee + system_fee_tax, transaction_fee_adyen_balance_account_id},
        {"kickback", promoter_kickback + promoter_kickback_tax, @balance_account_id},
        {"promoter", promoter_total, @balance_account_id},
        {"donation", donation, default_donation_balance_account_id},
        {"future_demand", future_demand_fee + future_demand_fee_tax, future_demand_fee_adyen_balance_account_id}
      ]

      expected_splits =
        Enum.map(split_attributes, fn {type, value, account_id} ->
          %{
            "type" => "Commission",
            "account" => account_id,
            "reference" => "#{type}-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => value
            }
          }
        end)

      # then filter all items with zero value
      expected_splits = Enum.filter(expected_splits, fn x -> x["amount"]["value"] > 0 end)

      split = RefundSplitCalculator.calculate_splits(bill, ticket, @balance_account_id, config)

      assert Enum.sort_by(split, & &1["reference"]) == Enum.sort_by(expected_splits, & &1["reference"])
    end

    # validate tickets are refundable

    test "validate_multiple_ticket_refundable/1  returns :ok if all tickets are refundable" do
      tickets = Factory.build_list(5, :ticket)
      assert RefundValidator.validate_tickets_refundable(tickets) == :ok
    end

    test "validate_multiple_ticket_refundable/1  returns {:error, error} if any ticket is not refundable" do
      tickets = Factory.build_list(3, :ticket)
      ticket = Factory.build(:ticket, status: :USED)
      tickets = [ticket | tickets]
      assert RefundValidator.validate_tickets_refundable(tickets) == {:error, :already_checked_in}
    end

    test "list_refund_transactions_by_ticket_and_ticket_group_ids/1 returns refund_transactions by tickets_ids" do
      refund_transaction = refund_transaction_fixture()
      result = Refunds.list_refund_transactions_by_ticket_and_ticket_group_ids([refund_transaction.refund_item_id], [])
      assert [] == result
    end

    test "create_refund_transaction/1 with valid data creates a refund_transaction" do
      valid_attrs = %{
        "currency" => "EUR",
        "amount" => 42,
        "payment_method" => "Visa",
        "actor_id" => "actor_id",
        "psp" => "Adyen",
        "psp_reference" => "Adyen",
        "psp_result" => %{"psp_result" => "some psp_result"},
        "reason" => "FAILURE_OR_GOODWILL_PROMOTER",
        "comment" => "REFUND INVITATION TICKET",
        "status" => "COMPLETED",
        "refund_item_type" => "TICKET",
        "refund_item_id" => "7488a646-e31f-11e4-aace-600308960662"
      }

      assert {:ok, %RefundTransaction{} = refund_transaction} =
               RefundTransactionManager.create_refund_transaction(valid_attrs)

      assert refund_transaction.status == :COMPLETED
      assert refund_transaction.currency == "EUR"
      assert refund_transaction.psp_reference == "Adyen"
      assert refund_transaction.amount == 42
      assert refund_transaction.payment_method == "Visa"
      assert refund_transaction.refund_item_id == "7488a646-e31f-11e4-aace-600308960662"
      assert refund_transaction.psp == "Adyen"
      assert refund_transaction.reason == :FAILURE_OR_GOODWILL_PROMOTER
    end

    test "create_refund_transaction/1 with invalid data returns error changeset" do
      assert {:error, {:insert_refund_transaction, %Ecto.Changeset{}}} =
               RefundTransactionManager.create_refund_transaction(@invalid_attrs)
    end
  end

  describe "history-based status reversion" do
    test "TicketHistory.get_previous_status_before_refunding/1 returns previous status when history exists" do
      # Create a ticket and simulate status changes
      ticket = Factory.insert(:ticket, status: :ACTIVE)

      # Simulate ticket going to REFUNDING state
      {:ok, _history} = TicketHistory.create(ticket, :REFUNDING, "test-user-id")

      # Get the previous status
      assert {:ok, :ACTIVE} = TicketHistory.get_previous_status_before_refunding(ticket.id)
    end

    test "TicketHistory.get_previous_status_before_refunding/1 returns error when no history exists" do
      ticket = Factory.insert(:ticket)

      assert {:error, :no_previous_status} = TicketHistory.get_previous_status_before_refunding(ticket.id)
    end

    test "OrderHistory.get_previous_status_before_refunding/1 returns previous status when history exists" do
      # Create an order and simulate status changes
      order = Factory.insert(:order, status: :PAID)

      # Simulate order going to REFUND_PENDING state
      {:ok, _history} = OrderHistory.create(order, :REFUND_PENDING, "test-user-id")

      # Get the previous status
      assert {:ok, :PAID} = OrderHistory.get_previous_status_before_refunding(order.id)
    end

    test "OrderHistory.get_previous_status_before_refunding/1 returns error when no history exists" do
      order = Factory.insert(:order)

      assert {:error, :no_previous_status} = OrderHistory.get_previous_status_before_refunding(order.id)
    end

    test "revert_refund_transaction/3 restores ticket to previous status from history" do
      # Create a ticket with ACTIVE status
      ticket = Factory.insert(:ticket, status: :ACTIVE)

      # Create history entry showing ticket went from ACTIVE to REFUNDING
      {:ok, _history} = TicketHistory.create(ticket, :REFUNDING, "test-user-id")

      # Update ticket to REFUNDING status to simulate current state
      {:ok, refunding_ticket} = TicketDB.set_status(ticket, :REFUNDING, "test-user-id", nil, nil, false, %{})

      # Create a refund transaction
      refund_transaction =
        Factory.insert(:refund_transaction,
          refund_item_type: :TICKET,
          refund_item_id: refunding_ticket.id,
          actor_id: "test-user-id"
        )

      # Revert the refund transaction
      assert {:ok, reverted_ticket} =
               RefundTransactionManager.revert_refund_transaction(
                 refunding_ticket,
                 refund_transaction,
                 %{"test" => "response"}
               )

      # Verify ticket status was restored to ACTIVE (the previous status)
      assert reverted_ticket.status == :ACTIVE
    end

    test "revert_refund_transaction/3 falls back to ACTIVE when no ticket history exists" do
      # Create a ticket in REFUNDING status without history
      ticket = Factory.insert(:ticket, status: :REFUNDING)

      # Create a refund transaction
      refund_transaction =
        Factory.insert(:refund_transaction,
          refund_item_type: :TICKET,
          refund_item_id: ticket.id,
          actor_id: "test-user-id"
        )

      # Revert the refund transaction
      assert {:ok, reverted_ticket} =
               RefundTransactionManager.revert_refund_transaction(
                 ticket,
                 refund_transaction,
                 %{"test" => "response"}
               )

      # Verify ticket status was set to ACTIVE (fallback)
      assert reverted_ticket.status == :ACTIVE
    end

    test "revert_refund_transaction/3 restores order to previous status from history" do
      # Create an order with PAID status
      order = Factory.insert(:order, status: :PAID)

      # Create history entry showing order went from PAID to REFUND_PENDING
      {:ok, _history} = OrderHistory.create(order, :REFUND_PENDING, "test-user-id")

      # Update order to REFUND_PENDING status to simulate current state
      {:ok, refunding_order} = Order.set_status(order, :REFUND_PENDING, "test-user-id")

      # Create a refund transaction
      refund_transaction =
        Factory.insert(:refund_transaction,
          refund_item_type: :ORDER,
          refund_item_id: refunding_order.id,
          actor_id: "test-user-id"
        )

      # Revert the refund transaction
      assert {:ok, reverted_order} =
               RefundTransactionManager.revert_refund_transaction(
                 refunding_order,
                 refund_transaction,
                 %{"test" => "response"}
               )

      # Verify order status was restored to PAID (the previous status)
      assert reverted_order.status == :PAID
    end

    test "revert_refund_transaction/3 falls back to TIMEDOUT when no order history exists" do
      # Create an order in REFUND_PENDING status without history
      order = Factory.insert(:order, status: :REFUND_PENDING)

      # Create a refund transaction
      refund_transaction =
        Factory.insert(:refund_transaction,
          refund_item_type: :ORDER,
          refund_item_id: order.id,
          actor_id: "test-user-id"
        )

      # Revert the refund transaction
      assert {:ok, reverted_order} =
               RefundTransactionManager.revert_refund_transaction(
                 order,
                 refund_transaction,
                 %{"test" => "response"}
               )

      # Verify order status was set to TIMEDOUT (fallback)
      assert reverted_order.status == :TIMEDOUT
    end
  end
end
