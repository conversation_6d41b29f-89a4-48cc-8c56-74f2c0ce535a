defmodule OrdersService.RefundsFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `OrdersService.Refunds` context.
  """

  @doc """
  Generate a refund_transaction.
  """
  def refund_transaction_fixture(attrs \\ %{}) do
    {:ok, refund_transaction} =
      attrs
      |> Enum.into(%{
        "currency" => "EUR",
        "amount" => 42,
        "payment_method" => "Visa",
        "actor_id" => "actor_id",
        "psp" => "Adyen",
        "psp_reference" => "Adyen",
        "psp_result" => %{"psp_result" => "some psp_result"},
        "reason" => "FAILURE_OR_GOODWILL_PROMOTER",
        "comment" => "REFUND INVITATION TICKET",
        "status" => "COMPLETED",
        "refund_item_type" => "TICKET",
        "refund_item_id" => "7488a646-e31f-11e4-aace-600308960662"
      })
      |> OrdersService.Refunds.RefundTransactionManager.create_refund_transaction()

    refund_transaction
  end
end
